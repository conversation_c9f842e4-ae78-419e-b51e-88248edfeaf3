import ReactDOM from 'react-dom';
import dva from 'dva';
import 'moment/locale/zh-cn';
import { message } from 'tntd';
import history, { createHistory } from '@/utils/history';
import { isAppListVisible, isOrgListVisible, isOrgAppListUnVisible, isEmptyLayout } from '@/utils/utils';
import router from './router';
import './styles/style.less';

const app = dva({
    history,
    onError(e) {
        message.error(e.message, 3);
    }
});

const registerModels = (app) => {
    app.model(require('./models/global').default);
    app.model(require('./models/login').default);
    app.model(require('./models/user').default);

    // 风险大盘
    app.model(require('./models/Dashboard/dbHome').default);
    app.model(require('@/routes/Dashboard/BeforeCredit/model/beforeCredit.js').default);
    app.model(require('./models/Dashboard/afterCredit').default);
    app.model(require('@/routes/Dashboard/CaseAnalysis/model/caseAnalysis.js').default);

    // 决策流引擎
    app.model(require('./models/Bodyguard/product').default);
    app.model(require('./models/workflow/workflowSet').default);

    // 进件管理
    app.model(require('./models/Entry/entry').default);
    app.model(require('./models/Entry/abnormal').default);
    app.model(require('./models/Entry/report').default);
    app.model(require('./models/Entry/persionEntry').default);
    app.model(require('./models/Entry/batchEntryDetail').default);
    app.model(require('./models/Entry/sampleLibrary').default);

    // 风险核查
    app.model(require('./models/Examine/examine').default);
};
registerModels(app);
if (!window.isInLightBox) {
    app.router(router);
    app.start('#root');
}

export async function bootstrap() {
    console.log('react app bootstrapped');
}

let onPopstate = (actions) => {
    actions?.setAppListVisible(isAppListVisible());
    actions.setOrgListVisible(isOrgListVisible()); // 机构可见
    actions.setOrgAppListVisible(!isOrgAppListUnVisible()); // 机构下的渠道可见
};

const initVisible = (actions) => {
    actions.setAppListVisible(isAppListVisible());
    actions.setOrgListVisible(isOrgListVisible()); // 机构可见
    actions.setOrgAppListVisible(!isOrgAppListUnVisible()); // 机构下的渠道可见
    actions.setIsEmptyLayout(isEmptyLayout()); // 空layou
};

// ！！！ 重要
export const mount = async ({ actions }) => {
    console.log('Credit app mount started', { actions });

    // 检查容器元素是否存在
    const container = document.getElementById('root');
    console.log('Container element:', container);

    if (!container) {
        console.error('Container element #root not found!');
        throw new Error('Container element #root not found');
    }

    window.__lightBoxActions__ = actions;

    initVisible(actions);
    onPopstate = onPopstate.bind(null, actions);
    window.addEventListener('popstate', onPopstate);

    // registerModels(app);
    // app.router(router);
    // 这里每次挂载时需要重新创建history对象，
    // 解决二次挂载时用到了前一次挂载的history对象而导致路由render异常问题
    app.router(({ app }) => router({ history: createHistory(), app, actions }));

    try {
        // 确保 DOM 完全准备好
        await new Promise(resolve => {
            if (document.readyState === 'complete') {
                resolve();
            } else {
                window.addEventListener('load', resolve);
            }
        });

        app.start('#root');
        console.log('Credit app started successfully');
    } catch (error) {
        console.error('Error starting credit app:', error);
        throw error;
    }
};

// ！！！ 重要
export const unmount = async () => {
    window.removeEventListener('popstate', onPopstate);

    // eslint-disable-next-line
    ReactDOM.unmountComponentAtNode(document.getElementById('root'));
    // app._models.forEach(model => {
    //   app.unmodel(model.namespace);
    // });
};

export async function update(props) {
    console.log('update props', props);
}

export const getAppStore = () => app._store;

export const getLanguage = () => {
    const globalStore = getAppStore().getState().global;
    const { personalMode } = globalStore;

    return personalMode.lang === 'cn' ? 'cn' : 'en';
};

export default app._store;
